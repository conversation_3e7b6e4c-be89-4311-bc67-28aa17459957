// Brendhouse Admin Panel Security
// Bu fayl admin panelin təhlükəsizliyini artırır

class AdminSecurity {
    constructor() {
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15 dəqiqə
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 saat
        this.init();
    }

    init() {
        // Brute force qorunması
        this.setupBruteForceProtection();
        
        // Session timeout
        this.setupSessionTimeout();
        
        // Developer tools aşkarlanması
        this.setupDevToolsDetection();
        
        // Right-click qadağası
        this.disableRightClick();
        
        // Console warning
        this.showConsoleWarning();
    }

    // Brute force qorunması
    setupBruteForceProtection() {
        const attemptsKey = 'brendhouse_login_attempts';
        const lockoutKey = 'brendhouse_lockout_until';

        // Login cəhdlərini yoxla
        window.checkLoginAttempts = () => {
            const lockoutUntil = localStorage.getItem(lockoutKey);
            if (lockoutUntil && Date.now() < parseInt(lockoutUntil)) {
                const remainingTime = Math.ceil((parseInt(lockoutUntil) - Date.now()) / 60000);
                return {
                    locked: true,
                    remainingMinutes: remainingTime
                };
            }
            return { locked: false };
        };

        // Uğursuz login cəhdini qeyd et
        window.recordFailedLogin = () => {
            const attempts = parseInt(localStorage.getItem(attemptsKey) || '0') + 1;
            localStorage.setItem(attemptsKey, attempts.toString());

            if (attempts >= this.maxLoginAttempts) {
                const lockoutUntil = Date.now() + this.lockoutDuration;
                localStorage.setItem(lockoutKey, lockoutUntil.toString());
                localStorage.removeItem(attemptsKey);
                
                return {
                    locked: true,
                    remainingMinutes: Math.ceil(this.lockoutDuration / 60000)
                };
            }

            return {
                locked: false,
                attemptsRemaining: this.maxLoginAttempts - attempts
            };
        };

        // Uğurlu login
        window.recordSuccessfulLogin = () => {
            localStorage.removeItem(attemptsKey);
            localStorage.removeItem(lockoutKey);
        };
    }

    // Session timeout
    setupSessionTimeout() {
        let lastActivity = Date.now();
        const inactivityTimeout = 30 * 60 * 1000; // 30 dəqiqə

        // Aktivitəni izlə
        const updateActivity = () => {
            lastActivity = Date.now();
        };

        document.addEventListener('mousedown', updateActivity);
        document.addEventListener('keydown', updateActivity);
        document.addEventListener('scroll', updateActivity);

        // Hər dəqiqə yoxla
        setInterval(() => {
            if (Date.now() - lastActivity > inactivityTimeout) {
                if (localStorage.getItem('brendhouse_admin_session')) {
                    alert('Session timeout! Yenidən login olmalısınız.');
                    if (typeof logout === 'function') {
                        logout();
                    }
                }
            }
        }, 60000);
    }

    // Developer tools aşkarlanması
    setupDevToolsDetection() {
        let devtools = false;
        
        const detectDevTools = () => {
            if (window.outerHeight - window.innerHeight > 200 || 
                window.outerWidth - window.innerWidth > 200) {
                if (!devtools) {
                    devtools = true;
                    console.clear();
                    console.log('%c⚠️ XƏBƏRDARLIQ!', 'color: red; font-size: 30px; font-weight: bold;');
                    console.log('%cBu admin panelidir! İcazəsiz giriş qadağandır!', 'color: red; font-size: 16px;');
                    
                    // Optional: logout et
                    // if (typeof logout === 'function') logout();
                }
            } else {
                devtools = false;
            }
        };

        setInterval(detectDevTools, 1000);
    }

    // Right-click qadağası
    disableRightClick() {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            console.log('Right-click qadağandır!');
        });

        // F12, Ctrl+Shift+I, Ctrl+U qadağası
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                console.log('Developer tools qadağandır!');
            }
        });
    }

    // Console warning
    showConsoleWarning() {
        console.clear();
        console.log('%c🔒 BRENDHOUSE ADMIN PANEL', 'color: #007bff; font-size: 24px; font-weight: bold;');
        console.log('%c⚠️ XƏBƏRDARLIQ: Bu admin panelidir!', 'color: red; font-size: 18px; font-weight: bold;');
        console.log('%cİcazəsiz giriş qadağandır və qanuni təqib ediləcək!', 'color: red; font-size: 14px;');
        console.log('%cBütün aktivitələr qeyd edilir və izlənir.', 'color: orange; font-size: 12px;');
    }

    // IP və browser məlumatlarını qeyd et
    logSecurityEvent(eventType, details = {}) {
        const securityLog = {
            timestamp: Date.now(),
            eventType: eventType,
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            url: window.location.href,
            referrer: document.referrer,
            ...details
        };

        // Local storage-a yaz
        const securityLogs = JSON.parse(localStorage.getItem('brendhouse_security_logs') || '[]');
        securityLogs.push(securityLog);
        
        // Maksimum 1000 log saxla
        if (securityLogs.length > 1000) {
            securityLogs.splice(0, securityLogs.length - 1000);
        }
        
        localStorage.setItem('brendhouse_security_logs', JSON.stringify(securityLogs));

        // Server-ə göndər (əgər API varsa)
        if (typeof fetch !== 'undefined') {
            fetch('/analytics/security', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(securityLog)
            }).catch(() => {
                // API yoxdursa, local storage kifayətdir
            });
        }
    }

    // Təhlükəsizlik loglarını göstər
    getSecurityLogs() {
        return JSON.parse(localStorage.getItem('brendhouse_security_logs') || '[]');
    }

    // Təhlükəsizlik loglarını təmizlə
    clearSecurityLogs() {
        localStorage.removeItem('brendhouse_security_logs');
        console.log('Təhlükəsizlik logları təmizləndi');
    }
}

// Security sistemini başlat
const adminSecurity = new AdminSecurity();

// Global funksiyalar
window.getSecurityLogs = () => adminSecurity.getSecurityLogs();
window.clearSecurityLogs = () => adminSecurity.clearSecurityLogs();

// Səhifə yüklənəndə security event qeyd et
document.addEventListener('DOMContentLoaded', () => {
    adminSecurity.logSecurityEvent('page_load', {
        pageTitle: document.title
    });
});

// Səhifədən çıxarkən security event qeyd et
window.addEventListener('beforeunload', () => {
    adminSecurity.logSecurityEvent('page_unload');
});

// Login cəhdlərini izlə
document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', (e) => {
            const lockoutStatus = window.checkLoginAttempts();
            if (lockoutStatus.locked) {
                e.preventDefault();
                alert(`Çox sayda yanlış cəhd! ${lockoutStatus.remainingMinutes} dəqiqə gözləyin.`);
                return;
            }
        });
    }
});

console.log('🔒 Admin Security System aktiv');
