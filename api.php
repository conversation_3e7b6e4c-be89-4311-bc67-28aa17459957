<?php
// Brendhouse Analytics PHP Proxy
// cPanel-də Node.js portları bloklandıqda istifadə üçün

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// OPTIONS request üçün
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Node.js server URL
$nodeServer = 'http://localhost:3015';

// Request path təyin et
$path = $_GET['path'] ?? '';
if (empty($path)) {
    $path = 'health'; // Default health check
}

// Full URL
$url = $nodeServer . '/' . $path;

// Request method
$method = $_SERVER['REQUEST_METHOD'];

// cURL ilə Node.js serverə request göndər
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);

// POST data varsa
if ($method === 'POST') {
    $postData = file_get_contents('php://input');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($postData)
    ]);
}

// Response al
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// Error handling
if ($error) {
    http_response_code(503);
    echo json_encode([
        'status' => 'error',
        'message' => 'Node.js server əlçatan deyil',
        'error' => $error
    ]);
    exit();
}

// Response göndər
http_response_code($httpCode);
echo $response;
?>
