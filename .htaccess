# Brendhouse Security Configuration

# HTTPS yönləndirmə
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# API proxy - Node.js app-a yönləndir (cPanel üçün)
RewriteRule ^api/(.*)$ http://localhost:3001/$1 [P,L]

# Directory browsing qadağası
Options -Indexes

# Admin panel təhlükəsizlik başlıqları
<Files "admin.html">
    <IfModule mod_headers.c>
        Header always set X-Frame-Options "DENY"
        Header always set X-Content-Type-Options "nosniff"
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
</Files>

# Sensitive faylları gizlət
<FilesMatch "\.(htaccess|htpasswd|env|log|json)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/javascript
</IfModule>
