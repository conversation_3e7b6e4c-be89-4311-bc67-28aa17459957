# Brendhouse Security Configuration

# Admin panel qorunması
<Files "admin.html">
    # IP əsaslı məhdudiyyət (öz IP-nizi əlavə edin)
    # Require ip *************
    # Require ip 10.0.0.0/8
    
    # User-Agent yoxlaması
    SetEnvIfNoCase User-Agent "bot|crawler|spider|scraper" bad_bot
    Order Allow,<PERSON>y
    Allow from all
    Deny from env=bad_bot
    
    # Referrer yoxlaması
    SetEnvIfNoCase Referer "^$" direct_access
    SetEnvIfNoCase Referer "brendhouse\.az" allowed_referrer
    
    # Rate limiting headers
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</Files>

# Admin security faylları qorunması
<Files "admin-security.js">
    Order Allow,<PERSON>y
    Allow from all
    
    # Cache control
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</Files>

# Analytics data qorunması
<FilesMatch "\.(json|log)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Server məlumatlarını gizlət
ServerTokens Prod
Header unset Server
Header unset X-Powered-By

# HTTPS yönləndirmə
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Admin panel üçün HTTPS məcburi
RewriteCond %{REQUEST_URI} ^/admin\.html$ [NC]
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Suspicious faylları blok et
<FilesMatch "\.(php|asp|aspx|cgi|pl)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Directory browsing qadağası
Options -Indexes

# Sensitive faylları gizlət
<FilesMatch "^(\.htaccess|\.htpasswd|\.env|config\.php|wp-config\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Rate limiting (mod_evasive lazımdır)
# <IfModule mod_evasive24.c>
#     DOSHashTableSize    2048
#     DOSPageCount        10
#     DOSPageInterval     1
#     DOSSiteCount        50
#     DOSSiteInterval     1
#     DOSBlockingPeriod   600
# </IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
