// Brendhouse Analytics System
// Bu sistem sayt ziyarətlərini və link kliklərini izləyir

class BrendhouseAnalytics {
    constructor() {
        // API URL - əgər localhost-da test edirsinizsə
        this.apiUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
            ? 'http://localhost:3000/analytics'
            : '/analytics'; // Production-da relative path

        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        this.isInitialized = false;
        this.apiAvailable = false;

        this.init();
    }

    // Unikal session ID yaradır
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Analytics sistemini başladır
    async init() {
        try {
            // API status yoxla
            await this.checkAPIStatus();

            // Səhifə yüklənmə<PERSON> qeyd et
            this.trackPageView();

            // Link kliklərini izlə
            this.setupClickTracking();

            // Səhifədən çıxışı izlə
            this.setupExitTracking();

            // Theme dəyişikliklərini izlə
            this.setupThemeTracking();

            // Əgər API mövcuddursa, localStorage-dan pending məlumatları göndər
            if (this.apiAvailable) {
                this.syncPendingData();
            }

            this.isInitialized = true;
            console.log('📊 Brendhouse Analytics başladıldı',
                       this.apiAvailable ? '(API aktiv)' : '(yalnız localStorage)');
        } catch (error) {
            console.error('Analytics xətası:', error);
        }
    }

    // Səhifə ziyarətini qeyd edir
    trackPageView() {
        const data = {
            type: 'page_view',
            sessionId: this.sessionId,
            timestamp: Date.now(),
            url: window.location.href,
            referrer: document.referrer || 'direct',
            userAgent: navigator.userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };

        this.sendData(data);
        
        // Local storage-a da yaz (backup üçün)
        this.saveToLocalStorage('page_views', data);
    }

    // Link kliklərini izləyir
    setupClickTracking() {
        // Bütün linkləri izlə
        document.addEventListener('click', (event) => {
            const target = event.target.closest('a');
            if (target) {
                this.trackLinkClick(target, event);
            }
        });
    }

    // Link klik məlumatlarını qeyd edir
    trackLinkClick(linkElement, event) {
        const linkType = this.getLinkType(linkElement);
        const linkUrl = linkElement.href;
        const linkText = linkElement.textContent.trim();
        
        const data = {
            type: 'link_click',
            sessionId: this.sessionId,
            timestamp: Date.now(),
            linkType: linkType,
            linkUrl: linkUrl,
            linkText: linkText,
            className: linkElement.className,
            position: this.getElementPosition(linkElement)
        };

        this.sendData(data);
        this.saveToLocalStorage('link_clicks', data);
        
        // Console-da göstər
        console.log(`🔗 Klik: ${linkType} - ${linkText}`);
    }

    // Link tipini müəyyən edir
    getLinkType(linkElement) {
        const className = linkElement.className;
        const href = linkElement.href;
        
        if (className.includes('phone-btn')) return 'phone';
        if (className.includes('whatsapp-btn')) return 'whatsapp';
        if (className.includes('website-btn')) return 'website';
        if (className.includes('instagram-btn')) return 'instagram';
        if (className.includes('facebook-btn')) return 'facebook';
        if (className.includes('tiktok-btn')) return 'tiktok';
        if (className.includes('youtube-btn')) return 'youtube';
        if (className.includes('linkedin-btn')) return 'linkedin';
        
        // URL-ə görə təyin et
        if (href.includes('tel:')) return 'phone';
        if (href.includes('wa.me')) return 'whatsapp';
        if (href.includes('instagram.com')) return 'instagram';
        if (href.includes('facebook.com')) return 'facebook';
        if (href.includes('tiktok.com')) return 'tiktok';
        if (href.includes('youtube.com')) return 'youtube';
        if (href.includes('linkedin.com')) return 'linkedin';
        if (href.includes('brendhouse.az')) return 'website';
        
        return 'other';
    }

    // Elementin səhifədəki mövqeyini alır
    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            x: Math.round(rect.left + window.scrollX),
            y: Math.round(rect.top + window.scrollY),
            width: Math.round(rect.width),
            height: Math.round(rect.height)
        };
    }

    // Səhifədən çıxışı izləyir
    setupExitTracking() {
        window.addEventListener('beforeunload', () => {
            const sessionDuration = Date.now() - this.startTime;
            
            const data = {
                type: 'session_end',
                sessionId: this.sessionId,
                timestamp: Date.now(),
                duration: sessionDuration
            };

            // Sync request (səhifə bağlanarkən)
            this.sendDataSync(data);
            this.saveToLocalStorage('session_ends', data);
        });
    }

    // Theme dəyişikliklərini izləyir
    setupThemeTracking() {
        // Theme toggle düyməsini izlə
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                const data = {
                    type: 'theme_change',
                    sessionId: this.sessionId,
                    timestamp: Date.now(),
                    fromTheme: currentTheme,
                    toTheme: newTheme
                };

                this.sendData(data);
                this.saveToLocalStorage('theme_changes', data);
            });
        }
    }

    // API status yoxla
    async checkAPIStatus() {
        try {
            const healthUrl = this.apiUrl.replace('/analytics', '/health');
            console.log('API health check:', healthUrl);

            const response = await fetch(healthUrl, {
                method: 'GET'
            });

            this.apiAvailable = response.ok;
            console.log('API Status:', this.apiAvailable ? 'Aktiv' : 'Deaktiv');

            if (response.ok) {
                const data = await response.json();
                console.log('API Response:', data);
            }
        } catch (error) {
            this.apiAvailable = false;
            console.warn('Analytics API əlçatan deyil:', error.message);
        }
    }

    // Pending məlumatları serverə göndər
    async syncPendingData() {
        if (!this.apiAvailable) return;

        try {
            const pendingData = JSON.parse(localStorage.getItem('brendhouse_analytics_failed_requests') || '[]');

            if (pendingData.length > 0) {
                console.log(`📤 ${pendingData.length} pending məlumat serverə göndərilir...`);

                for (const data of pendingData) {
                    await this.sendDataDirect(data); // direct göndər
                }

                // Uğurla göndərildikdən sonra pending data təmizlə
                localStorage.removeItem('brendhouse_analytics_failed_requests');
                console.log('✅ Pending məlumatlar serverə göndərildi');
            }
        } catch (error) {
            console.error('Pending data sync xətası:', error);
        }
    }

    // Məlumatları birbaşa serverə göndər (retry olmadan)
    async sendDataDirect(data) {
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
            mode: 'cors' // CORS mode əlavə et
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
    }

    // Məlumatları serverə göndərir (async)
    async sendData(data) {
        if (!this.apiAvailable) {
            // API mövcud deyilsə, birbaşa localStorage-a yaz
            console.log('📱 API mövcud deyil, localStorage-a yazılır:', data.type);
            this.saveToLocalStorage('failed_requests', data);
            return;
        }

        try {
            // Real API-yə göndər
            await this.sendDataDirect(data);
            console.log('📤 Məlumat serverə göndərildi:', data.type);
        } catch (error) {
            // API işləmirsə, local storage-a yaz
            console.warn('Analytics API xətası, local storage istifadə edilir:', error.message);
            this.saveToLocalStorage('failed_requests', data);

            // API status yenidən yoxla
            this.apiAvailable = false;
        }
    }

    // Məlumatları serverə göndərir (sync - səhifə bağlanarkən)
    sendDataSync(data) {
        try {
            // Navigator.sendBeacon istifadə et (daha etibarlı)
            if (navigator.sendBeacon) {
                const blob = new Blob([JSON.stringify(data)], {type: 'application/json'});
                navigator.sendBeacon(this.apiUrl, blob);
            } else {
                // Fallback: sync XMLHttpRequest
                const xhr = new XMLHttpRequest();
                xhr.open('POST', this.apiUrl, false); // sync
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(data));
            }
        } catch (error) {
            this.saveToLocalStorage('failed_sync_requests', data);
        }
    }

    // Local storage-a məlumat yazar
    saveToLocalStorage(category, data) {
        try {
            const key = `brendhouse_analytics_${category}`;
            let existingData = JSON.parse(localStorage.getItem(key) || '[]');
            
            existingData.push(data);
            
            // Maksimum 1000 qeyd saxla (performans üçün)
            if (existingData.length > 1000) {
                existingData = existingData.slice(-1000);
            }
            
            localStorage.setItem(key, JSON.stringify(existingData));
        } catch (error) {
            console.warn('Local storage yazma xətası:', error);
        }
    }

    // Analytics məlumatlarını əldə edir
    getAnalyticsData() {
        const categories = ['page_views', 'link_clicks', 'session_ends', 'theme_changes', 'failed_requests'];
        const data = {};
        
        categories.forEach(category => {
            const key = `brendhouse_analytics_${category}`;
            data[category] = JSON.parse(localStorage.getItem(key) || '[]');
        });
        
        return data;
    }

    // Analytics məlumatlarını təmizləyir
    clearAnalyticsData() {
        const categories = ['page_views', 'link_clicks', 'session_ends', 'theme_changes', 'failed_requests'];
        
        categories.forEach(category => {
            const key = `brendhouse_analytics_${category}`;
            localStorage.removeItem(key);
        });
        
        console.log('📊 Analytics məlumatları təmizləndi');
    }

    // Statistika göstərir
    showStats() {
        const data = this.getAnalyticsData();
        
        console.group('📊 Brendhouse Analytics Statistikası');
        console.log('Səhifə ziyarətləri:', data.page_views.length);
        console.log('Link klikləri:', data.link_clicks.length);
        console.log('Session bitirmələri:', data.session_ends.length);
        console.log('Theme dəyişiklikləri:', data.theme_changes.length);
        
        // Link klik statistikası
        const linkStats = {};
        data.link_clicks.forEach(click => {
            linkStats[click.linkType] = (linkStats[click.linkType] || 0) + 1;
        });
        
        console.log('Link klik statistikası:', linkStats);
        console.groupEnd();
        
        return data;
    }
}

// Analytics sistemini başlat
window.brendhouseAnalytics = new BrendhouseAnalytics();

// Global funksiyalar (console-dan istifadə üçün)
window.showAnalyticsStats = () => window.brendhouseAnalytics.showStats();
window.clearAnalyticsData = () => window.brendhouseAnalytics.clearAnalyticsData();
window.getAnalyticsData = () => window.brendhouseAnalytics.getAnalyticsData();
