/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;
    backdrop-filter: blur(10px);
}

/* Logo Section */
.logo-section {
    margin-bottom: 40px;
}

.logo {
    max-width: 200px;
    height: auto;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Contact Section */
.contact-section {
    margin-bottom: 40px;
}

.contact-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.phone-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.phone-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.whatsapp-btn {
    background: linear-gradient(45deg, #25D366, #128C7E);
    color: white;
}

.whatsapp-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
}

/* Social Section */
.social-section h3 {
    color: #333;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 700;
}

.social-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.social-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 15px;
    border-radius: 15px;
    text-decoration: none;
    color: white;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-btn i {
    font-size: 24px;
}

.website-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.instagram-btn {
    background: linear-gradient(45deg, #E4405F, #C13584);
}

.facebook-btn {
    background: linear-gradient(45deg, #1877F2, #42A5F5);
}

.tiktok-btn {
    background: linear-gradient(45deg, #000000, #333333);
}

.youtube-btn {
    background: linear-gradient(45deg, #FF0000, #CC0000);
}

.social-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Footer */
.footer {
    color: #666;
    font-size: 14px;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .social-links {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .logo {
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .social-links {
        grid-template-columns: 1fr;
    }
    
    .social-btn {
        flex-direction: row;
        justify-content: center;
    }
}
