/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme */
    --bg-color: #f8f9fa;
    --container-bg: #ffffff;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #e9ecef;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --phone-color: #28a745;
    --whatsapp-color: #25d366;
    --website-color: #6f42c1;
    --instagram-color: #e4405f;
    --facebook-color: #1877f2;
    --tiktok-color: #000000;
    --youtube-color: #ff0000;
    --linkedin-color: #0077b5;
}

[data-theme="dark"] {
    /* Dark Theme */
    --bg-color: #1a1a1a;
    --container-bg: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #adb5bd;
    --border-color: #404040;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0;
    transition: background-color 0.3s ease, color 0.3s ease;
}



/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    background: var(--container-bg);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: var(--shadow);
    z-index: 1000;
}

.theme-toggle:hover {
    transform: scale(1.05);
}

.toggle-icon {
    position: relative;
    width: 20px;
    height: 20px;
}

.sun-icon, .moon-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    transition: all 0.3s ease;
}

.sun-icon {
    color: #ffa500;
    opacity: 1;
}

.moon-icon {
    color: #4a90e2;
    opacity: 0;
}

[data-theme="dark"] .sun-icon {
    opacity: 0;
}

[data-theme="dark"] .moon-icon {
    opacity: 1;
}

@keyframes themeToggleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

@keyframes themeChangeRipple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}



.container {
    background: var(--container-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 40px;
    box-shadow: var(--shadow);
    text-align: center;
    max-width: 480px;
    width: 100%;
    transition: all 0.3s ease;
}



/* Logo Section */
.logo-section {
    margin-bottom: 40px;
}

.logo {
    max-width: 180px;
    height: auto;
    border-radius: 12px;
    transition: transform 0.2s ease;
}

.logo:hover {
    transform: scale(1.02);
}

/* Contact Section */
.contact-section {
    margin-bottom: 40px;
}

.contact-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 14px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    color: white;
    transition: all 0.2s ease;
    box-shadow: var(--shadow);
    border: none;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.phone-btn {
    background: var(--phone-color);
}

.phone-btn:hover {
    background: #218838;
}

.whatsapp-btn {
    background: var(--whatsapp-color);
}

.whatsapp-btn:hover {
    background: #20c55a;
}

/* Social Section */
.social-section h3 {
    color: var(--text-primary);
    margin-bottom: 24px;
    font-size: 24px;
    font-weight: 600;
}

.social-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 30px;
}

.social-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border-radius: 12px;
    text-decoration: none;
    color: white;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.2s ease;
    box-shadow: var(--shadow);
    border: none;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.social-btn i {
    font-size: 24px;
}

.website-btn {
    background: var(--website-color);
}

.website-btn:hover {
    background: #5a32a3;
}

.instagram-btn {
    background: var(--instagram-color);
}

.instagram-btn:hover {
    background: #d62d51;
}

.facebook-btn {
    background: var(--facebook-color);
}

.facebook-btn:hover {
    background: #166fe5;
}

.tiktok-btn {
    background: var(--tiktok-color);
}

.tiktok-btn:hover {
    background: #333333;
}

.youtube-btn {
    background: var(--youtube-color);
}

.youtube-btn:hover {
    background: #cc0000;
}

.linkedin-btn {
    background: var(--linkedin-color);
}

.linkedin-btn:hover {
    background: #005885;
}

/* Focus states for accessibility */
.contact-btn:focus, .social-btn:focus, .theme-toggle:focus {
    outline: 2px solid #4a90e2;
    outline-offset: 2px;
}

/* Footer */
.footer {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.footer p {
    color: var(--text-secondary);
    font-size: 14px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 32px 24px;
        margin: 16px;
    }

    .contact-buttons {
        gap: 12px;
    }

    .contact-btn {
        flex: 1;
        max-width: 140px;
        padding: 12px 20px;
        font-size: 15px;
    }

    .social-btn {
        padding: 14px 10px;
        font-size: 12px;
    }

    .social-btn i {
        font-size: 22px;
    }

    .logo {
        max-width: 160px;
    }

    .social-section h3 {
        font-size: 22px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 28px 20px;
        margin: 12px;
    }

    .contact-btn {
        padding: 10px 16px;
        font-size: 14px;
    }

    .social-btn {
        padding: 12px 8px;
        font-size: 11px;
    }

    .social-btn i {
        font-size: 20px;
    }

    .social-section h3 {
        font-size: 20px;
        margin-bottom: 20px;
    }

    .logo {
        max-width: 140px;
    }

    .theme-toggle {
        width: 44px;
        height: 44px;
        top: 16px;
        right: 16px;
    }

    .toggle-icon {
        width: 18px;
        height: 18px;
    }

    .sun-icon, .moon-icon {
        font-size: 16px;
    }
}
