/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme Variables */
    --bg-gradient-1: #667eea;
    --bg-gradient-2: #764ba2;
    --bg-gradient-3: #f093fb;
    --container-bg: rgba(255, 255, 255, 0.1);
    --container-border: rgba(255, 255, 255, 0.2);
    --text-primary: #333;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-footer: rgba(255, 255, 255, 0.6);
    --shadow-color: rgba(0, 0, 0, 0.15);
    --particle-color: rgba(255, 255, 255, 0.6);
    --glow-color: rgba(102, 126, 234, 0.2);
}

[data-theme="dark"] {
    /* Dark Theme Variables */
    --bg-gradient-1: #0f0f23;
    --bg-gradient-2: #1a1a2e;
    --bg-gradient-3: #16213e;
    --container-bg: rgba(0, 0, 0, 0.3);
    --container-border: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-footer: rgba(255, 255, 255, 0.7);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --particle-color: rgba(100, 200, 255, 0.4);
    --glow-color: rgba(100, 200, 255, 0.3);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-gradient-1) 0%, var(--bg-gradient-2) 50%, var(--bg-gradient-3) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow-x: hidden;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, var(--glow-color) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    transition: all 0.5s ease;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: var(--container-bg);
    border: 1px solid var(--container-border);
    border-radius: 50%;
    backdrop-filter: blur(20px);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 25px var(--shadow-color),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    z-index: 1000;
    animation: themeToggleFloat 4s ease-in-out infinite;
}

.theme-toggle:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow:
        0 15px 35px var(--shadow-color),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.toggle-icon {
    position: relative;
    width: 24px;
    height: 24px;
}

.sun-icon, .moon-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.sun-icon {
    color: #ffd700;
    opacity: 1;
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
}

.moon-icon {
    color: #64b5f6;
    opacity: 0;
    transform: translate(-50%, -50%) rotate(180deg) scale(0);
}

[data-theme="dark"] .sun-icon {
    opacity: 0;
    transform: translate(-50%, -50%) rotate(-180deg) scale(0);
}

[data-theme="dark"] .moon-icon {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
}

@keyframes themeToggleFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

@keyframes themeChangeRipple {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Floating Particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--particle-color);
    border-radius: 50%;
    animation: float 20s infinite linear;
    transition: background 0.5s ease;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 15s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 2s;
    animation-duration: 18s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 4s;
    animation-duration: 22s;
}

.particle:nth-child(4) {
    left: 70%;
    animation-delay: 6s;
    animation-duration: 16s;
}

.particle:nth-child(5) {
    left: 80%;
    animation-delay: 8s;
    animation-duration: 20s;
}

.particle:nth-child(6) {
    left: 90%;
    animation-delay: 10s;
    animation-duration: 25s;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.container {
    background: var(--container-bg);
    border: 1px solid var(--container-border);
    border-radius: 30px;
    padding: 50px;
    box-shadow:
        0 25px 50px var(--shadow-color),
        0 0 0 1px var(--container-border),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    text-align: center;
    max-width: 520px;
    width: 100%;
    backdrop-filter: blur(20px);
    position: relative;
    animation: containerFloat 6s ease-in-out infinite;
    transform-style: preserve-3d;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0.1) 75%,
        rgba(255, 255, 255, 0.3) 100%);
    border-radius: 32px;
    z-index: -1;
    animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes containerFloat {
    0%, 100% { transform: translateY(0px) rotateX(0deg); }
    50% { transform: translateY(-10px) rotateX(2deg); }
}

@keyframes borderGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Logo Section */
.logo-section {
    margin-bottom: 50px;
    position: relative;
}

.logo-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
    animation: logoGlow 4s ease-in-out infinite;
}

.logo {
    max-width: 220px;
    height: auto;
    border-radius: 20px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
    position: relative;
    z-index: 1;
}

.logo:hover {
    transform: scale(1.08) translateY(-5px);
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
}

@keyframes logoGlow {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.8;
    }
}

/* Contact Section */
.contact-section {
    margin-bottom: 40px;
}

.contact-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 18px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.contact-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.contact-btn:hover::before {
    left: 100%;
}

.phone-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 50%, #66BB6A 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.phone-btn:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(76, 175, 80, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg, #66BB6A 0%, #4CAF50 50%, #45a049 100%);
}

.whatsapp-btn {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 50%, #075E54 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.whatsapp-btn:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(37, 211, 102, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    background: linear-gradient(135deg, #128C7E 0%, #25D366 50%, #34E79A 100%);
}

/* Social Section */
.social-section {
    position: relative;
}

.social-section h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 800;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
}

.social-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: underlineGlow 2s ease-in-out infinite alternate;
}

@keyframes underlineGlow {
    0% { width: 60px; opacity: 0.7; }
    100% { width: 80px; opacity: 1; }
}

.social-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.social-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 25px 18px;
    border-radius: 20px;
    text-decoration: none;
    color: white;
    font-weight: 700;
    font-size: 14px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.social-btn:hover::before {
    transform: translateX(100%);
}

.social-btn i {
    font-size: 28px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.social-btn:hover i {
    transform: scale(1.2) rotateY(360deg);
}

.website-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.website-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2 0%, #f093fb 50%, #667eea 100%);
}

.instagram-btn {
    background: linear-gradient(135deg, #E4405F 0%, #C13584 50%, #833AB4 100%);
}

.instagram-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(228, 64, 95, 0.4);
    background: linear-gradient(135deg, #833AB4 0%, #E4405F 50%, #F77737 100%);
}

.facebook-btn {
    background: linear-gradient(135deg, #1877F2 0%, #42A5F5 50%, #2196F3 100%);
}

.facebook-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(24, 119, 242, 0.4);
    background: linear-gradient(135deg, #2196F3 0%, #1877F2 50%, #0D47A1 100%);
}

.tiktok-btn {
    background: linear-gradient(135deg, #000000 0%, #333333 50%, #FF0050 100%);
}

.tiktok-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 0, 80, 0.4);
    background: linear-gradient(135deg, #FF0050 0%, #000000 50%, #00F2EA 100%);
}

.youtube-btn {
    background: linear-gradient(135deg, #FF0000 0%, #CC0000 50%, #8B0000 100%);
}

.youtube-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 0, 0, 0.4);
    background: linear-gradient(135deg, #8B0000 0%, #FF0000 50%, #FF4500 100%);
}

.linkedin-btn {
    background: linear-gradient(135deg, #0077B5 0%, #005885 50%, #003d5c 100%);
}

.linkedin-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 119, 181, 0.4);
    background: linear-gradient(135deg, #003d5c 0%, #0077B5 50%, #00A0DC 100%);
}

/* Dark Theme Enhancements */
[data-theme="dark"] .social-btn {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .contact-btn {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .container {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .theme-toggle {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced glow effects for dark theme */
[data-theme="dark"] .logo-section::before {
    background: radial-gradient(circle, rgba(100, 200, 255, 0.3) 0%, transparent 70%);
}

[data-theme="dark"] .social-section h3::after {
    background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
    box-shadow: 0 0 20px rgba(100, 181, 246, 0.5);
}

/* Scroll indicator */
.scroll-indicator {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 8px;
    animation: scrollIndicatorFloat 2s ease-in-out infinite;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.scroll-indicator::before {
    content: '';
    width: 4px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    animation: scrollDot 2s ease-in-out infinite;
}

@keyframes scrollIndicatorFloat {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes scrollDot {
    0% { transform: translateY(0px); opacity: 1; }
    50% { transform: translateY(15px); opacity: 0.3; }
    100% { transform: translateY(0px); opacity: 1; }
}

/* Enhanced button press effects */
.contact-btn:active, .social-btn:active {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease;
}

/* Improved focus states for accessibility */
.contact-btn:focus, .social-btn:focus, .theme-toggle:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 4px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--bg-gradient-1), var(--bg-gradient-2));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--bg-gradient-2), var(--bg-gradient-3));
}

/* Footer */
.footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.footer p {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 35px 25px;
        margin: 15px;
        border-radius: 25px;
    }

    .contact-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 15px;
    }

    .contact-btn {
        flex: 1;
        max-width: 140px;
        justify-content: center;
        padding: 15px 20px;
        font-size: 14px;
    }

    .social-links {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .social-btn {
        padding: 20px 12px;
        font-size: 12px;
        border-radius: 18px;
    }

    .social-btn i {
        font-size: 24px;
    }

    .logo {
        max-width: 180px;
    }

    .social-section h3 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 30px 20px;
        margin: 10px;
        border-radius: 20px;
    }

    .contact-btn {
        padding: 12px 18px;
        font-size: 13px;
    }

    .social-links {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }

    .social-btn {
        padding: 18px 10px;
        font-size: 11px;
        flex-direction: column;
        justify-content: center;
        border-radius: 15px;
    }

    .social-btn i {
        font-size: 22px;
        margin-bottom: 6px;
    }

    .social-section h3 {
        font-size: 22px;
        margin-bottom: 25px;
    }

    .logo {
        max-width: 160px;
    }

    /* Disable 3D effects on mobile for better performance */
    .container {
        transform: none !important;
    }

    .theme-toggle {
        top: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
    }

    .toggle-icon {
        width: 20px;
        height: 20px;
    }

    .sun-icon, .moon-icon {
        font-size: 16px;
    }

    .scroll-indicator {
        display: none;
    }
}
