<?php
// Simple Analytics Data Handler
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$action = $_GET['action'] ?? 'health';

switch($action) {
    case 'health':
        echo json_encode([
            'status' => 'ok',
            'message' => 'Analytics API işləyir',
            'timestamp' => date('c')
        ]);
        break;
        
    case 'save':
        $data = json_decode(file_get_contents('php://input'), true);
        if ($data) {
            $filename = 'analytics_' . date('Y-m-d') . '.json';
            $existing = [];
            if (file_exists($filename)) {
                $existing = json_decode(file_get_contents($filename), true) ?: [];
            }
            $existing[] = array_merge($data, ['timestamp' => time()]);
            file_put_contents($filename, json_encode($existing, JSON_PRETTY_PRINT));
            echo json_encode(['status' => 'saved']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No data']);
        }
        break;
        
    case 'stats':
        $files = glob('analytics_*.json');
        $total = 0;
        foreach($files as $file) {
            $data = json_decode(file_get_contents($file), true) ?: [];
            $total += count($data);
        }
        echo json_encode([
            'total_events' => $total,
            'files' => count($files)
        ]);
        break;
        
    default:
        echo json_encode(['status' => 'unknown_action']);
}
?>
