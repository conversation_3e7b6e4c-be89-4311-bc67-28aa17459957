// Brendhouse Analytics Backend Server
// Node.js + Express server nümunəsi

const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;
const DATA_DIR = './analytics-data';

// Middleware
app.use(cors());
app.use(express.json());

// Analytics data qovluğunu yarat
async function ensureDataDir() {
    try {
        await fs.access(DATA_DIR);
    } catch {
        await fs.mkdir(DATA_DIR, { recursive: true });
    }
}

// Məlumatları fayla yaz
async function saveToFile(filename, data) {
    try {
        const filePath = path.join(DATA_DIR, filename);
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            ...data
        };
        
        // Mövcud məlumatları oxu
        let existingData = [];
        try {
            const fileContent = await fs.readFile(filePath, 'utf8');
            existingData = JSON.parse(fileContent);
        } catch {
            // Fayl yoxdursa, boş array
        }
        
        // Yeni məlumatı əlavə et
        existingData.push(logEntry);
        
        // Maksimum 10000 qeyd saxla
        if (existingData.length > 10000) {
            existingData = existingData.slice(-10000);
        }
        
        // Fayla yaz
        await fs.writeFile(filePath, JSON.stringify(existingData, null, 2));
        
        return true;
    } catch (error) {
        console.error('Fayl yazma xətası:', error);
        return false;
    }
}

// Analytics məlumatlarını qəbul et
app.post('/analytics', async (req, res) => {
    try {
        const data = req.body;
        
        // Məlumat tipinə görə fərqli fayllara yaz
        let filename;
        switch (data.type) {
            case 'page_view':
                filename = 'page_views.json';
                break;
            case 'link_click':
                filename = 'link_clicks.json';
                break;
            case 'session_end':
                filename = 'session_ends.json';
                break;
            case 'theme_change':
                filename = 'theme_changes.json';
                break;
            default:
                filename = 'other_events.json';
        }
        
        const success = await saveToFile(filename, data);
        
        if (success) {
            res.status(200).json({ 
                status: 'success', 
                message: 'Məlumat qeyd edildi',
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(500).json({ 
                status: 'error', 
                message: 'Məlumat qeyd edilə bilmədi' 
            });
        }
        
    } catch (error) {
        console.error('Analytics xətası:', error);
        res.status(400).json({ 
            status: 'error', 
            message: 'Yanlış məlumat formatı' 
        });
    }
});

// API status yoxla
app.get('/analytics/status', (req, res) => {
    res.json({
        status: 'online',
        timestamp: new Date().toISOString(),
        server: 'Brendhouse Analytics API'
    });
});

// Statistika məlumatlarını al
app.get('/analytics/stats', async (req, res) => {
    try {
        const stats = {
            page_views: 0,
            link_clicks: 0,
            session_ends: 0,
            theme_changes: 0,
            link_stats: {},
            recent_activity: []
        };
        
        // Faylları oxu və statistika hesabla
        const files = [
            'page_views.json',
            'link_clicks.json', 
            'session_ends.json',
            'theme_changes.json'
        ];
        
        for (const file of files) {
            try {
                const filePath = path.join(DATA_DIR, file);
                const content = await fs.readFile(filePath, 'utf8');
                const data = JSON.parse(content);
                
                const category = file.replace('.json', '');
                stats[category] = data.length;
                
                // Link klik statistikası
                if (category === 'link_clicks') {
                    data.forEach(click => {
                        const linkType = click.linkType || 'other';
                        stats.link_stats[linkType] = (stats.link_stats[linkType] || 0) + 1;
                    });
                }
                
                // Son aktivitələr (son 50)
                stats.recent_activity.push(...data.slice(-50));
                
            } catch {
                // Fayl yoxdursa, 0 olaraq qal
            }
        }
        
        // Son aktivitələri vaxt üzrə sırala
        stats.recent_activity.sort((a, b) => 
            new Date(b.timestamp) - new Date(a.timestamp)
        ).slice(0, 100);
        
        res.json(stats);
        
    } catch (error) {
        console.error('Statistika xətası:', error);
        res.status(500).json({ 
            status: 'error', 
            message: 'Statistika alına bilmədi' 
        });
    }
});

// Məlumatları export et
app.get('/analytics/export', async (req, res) => {
    try {
        const exportData = {};
        
        const files = [
            'page_views.json',
            'link_clicks.json',
            'session_ends.json', 
            'theme_changes.json',
            'other_events.json'
        ];
        
        for (const file of files) {
            try {
                const filePath = path.join(DATA_DIR, file);
                const content = await fs.readFile(filePath, 'utf8');
                const category = file.replace('.json', '');
                exportData[category] = JSON.parse(content);
            } catch {
                exportData[file.replace('.json', '')] = [];
            }
        }
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 
            `attachment; filename=brendhouse-analytics-${new Date().toISOString().split('T')[0]}.json`);
        res.json(exportData);
        
    } catch (error) {
        console.error('Export xətası:', error);
        res.status(500).json({ 
            status: 'error', 
            message: 'Export edilə bilmədi' 
        });
    }
});

// Admin authentication middleware
function requireAdmin(req, res, next) {
    const { adminKey } = req.body;
    const authHeader = req.headers.authorization;

    // Body-dən və ya header-dən admin key yoxla
    const providedKey = adminKey || (authHeader && authHeader.replace('Bearer ', ''));

    if (providedKey !== 'brendhouse2024') {
        return res.status(401).json({
            status: 'error',
            message: 'Yanlış admin key - giriş qadağandır'
        });
    }

    next();
}

// Məlumatları təmizlə (admin üçün)
app.delete('/analytics/clear', requireAdmin, async (req, res) => {
    try {
        
        const files = [
            'page_views.json',
            'link_clicks.json',
            'session_ends.json',
            'theme_changes.json',
            'other_events.json'
        ];
        
        for (const file of files) {
            try {
                const filePath = path.join(DATA_DIR, file);
                await fs.unlink(filePath);
            } catch {
                // Fayl yoxdursa, problem yox
            }
        }
        
        res.json({ 
            status: 'success', 
            message: 'Bütün məlumatlar təmizləndi',
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('Təmizləmə xətası:', error);
        res.status(500).json({ 
            status: 'error', 
            message: 'Təmizləmə xətası' 
        });
    }
});

// Server başlat
async function startServer() {
    await ensureDataDir();
    
    app.listen(PORT, () => {
        console.log(`🚀 Brendhouse Analytics Server ${PORT} portunda işləyir`);
        console.log(`📊 Analytics endpoint: http://localhost:${PORT}/analytics`);
        console.log(`📈 Stats endpoint: http://localhost:${PORT}/analytics/stats`);
        console.log(`💾 Data directory: ${DATA_DIR}`);
    });
}

// Error handling
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Server başlat
startServer();

module.exports = app;
