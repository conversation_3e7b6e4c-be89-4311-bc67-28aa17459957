<!DOCTYPE html>
<html lang="az">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brendhouse Analytics Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }

        .qr-section {
            text-align: center;
        }

        .qr-code {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: inline-block;
        }

        .url-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            margin: 15px 0;
            word-break: break-all;
        }

        .link-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .link-stat {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #007bff;
        }

        .link-stat .name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .link-stat .count {
            font-size: 1.5em;
            color: #007bff;
            font-weight: bold;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .timestamp {
            font-size: 0.9em;
            color: #666;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status.online {
            background: #d4edda;
            color: #155724;
        }

        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }

        /* Login Modal */
        .modal {
            display: flex;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .modal-content h2 {
            color: #333;
            margin-bottom: 15px;
        }

        .modal-content p {
            color: #666;
            margin-bottom: 25px;
        }

        .modal-content input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        .modal-content input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .error-message {
            color: #dc3545;
            margin-top: 15px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .link-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .container {
                padding: 10px;
            }

            .modal-content {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Modal -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <h2>🔐 Admin Girişi</h2>
            <p>Analytics dashboard-a daxil olmaq üçün şifrəni daxil edin:</p>
            <form id="loginForm">
                <input type="password" id="passwordInput" placeholder="Admin şifrəsi" required>
                <button type="submit" class="btn">Daxil ol</button>
            </form>
            <div id="loginError" class="error-message"></div>
        </div>
    </div>

    <div class="container" id="dashboard" style="display: none;">
        <div class="header">
            <h1>📊 Brendhouse Analytics Dashboard</h1>
            <p>Sayt ziyarətləri və link klik statistikaları</p>
            <div style="margin-top: 15px;">
                <span class="status online" id="apiStatus">API Status: Yoxlanır...</span>
                <button class="btn btn-danger" onclick="logout()" style="margin-left: 15px;">Çıxış</button>
            </div>
        </div>

        <div class="dashboard">
            <!-- QR Kod Bölməsi -->
            <div class="card qr-section">
                <h3>🔗 QR Kod</h3>
                <div class="url-display">https://social.brendhouse.az</div>
                <div class="qr-code" id="qrCode">
                    QR kod yüklənir...
                </div>
                <button class="btn" onclick="downloadQR()">QR Kodu Yüklə</button>
                <button class="btn btn-success" onclick="copyURL()">Linki Kopyala</button>
            </div>

            <!-- Ümumi Statistika -->
            <div class="card">
                <h3>📈 Ümumi Statistika</h3>
                <div>
                    <strong>Səhifə Ziyarətləri:</strong>
                    <div class="stat-number" id="pageViews">0</div>
                </div>
                <div>
                    <strong>Link Klikləri:</strong>
                    <div class="stat-number" id="linkClicks">0</div>
                </div>
                <div>
                    <strong>Aktiv Sessionlar:</strong>
                    <div class="stat-number" id="activeSessions">0</div>
                </div>
            </div>

            <!-- Link Klik Statistikası -->
            <div class="card">
                <h3>🔗 Link Klik Statistikası</h3>
                <div class="link-stats" id="linkStats">
                    <!-- JavaScript ilə doldurulacaq -->
                </div>
            </div>
        </div>

        <!-- Son Aktivitələr -->
        <div class="card">
            <h3>⏰ Son Aktivitələr</h3>
            <button class="btn" onclick="refreshData()">Yenilə</button>
            <button class="btn btn-danger" onclick="clearAllData()">Bütün Məlumatları Təmizlə</button>
            <button class="btn" onclick="exportData()">Məlumatları Export Et</button>
            
            <table class="data-table" id="activityTable">
                <thead>
                    <tr>
                        <th>Vaxt</th>
                        <th>Tip</th>
                        <th>Detallar</th>
                        <th>Session ID</th>
                    </tr>
                </thead>
                <tbody id="activityTableBody">
                    <!-- JavaScript ilə doldurulacaq -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- QR Code Library with fallback -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"
            onerror="console.warn('QRCode library yüklənmədi, fallback istifadə ediləcək')"></script>

    <!-- Admin Security -->
    <script src="admin-security.js"></script>
    
    <script>
        // Dashboard JavaScript
        const SITE_URL = 'https://social.brendhouse.az';
        
        // QR kod yaradır
        function generateQR() {
            const qrContainer = document.getElementById('qrCode');

            // QRCode library yüklənib-yüklənmədiyini yoxla
            if (typeof QRCode === 'undefined') {
                console.error('QRCode library yüklənməyib');
                qrContainer.innerHTML = `
                    <div style="padding: 20px; background: #f8d7da; color: #721c24; border-radius: 8px;">
                        QR kod library yüklənmədi. <br>
                        <a href="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(SITE_URL)}" target="_blank">
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(SITE_URL)}"
                                 alt="QR Code" style="margin-top: 10px; border: 1px solid #ddd;">
                        </a>
                        <br><small>QR kod (online generator)</small>
                    </div>
                `;
                return;
            }

            try {
                QRCode.toCanvas(SITE_URL, {
                    width: 200,
                    height: 200,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function (error, canvas) {
                    if (error) {
                        console.error('QR kod xətası:', error);
                        // Fallback: online QR generator
                        qrContainer.innerHTML = `
                            <div style="text-align: center;">
                                <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(SITE_URL)}"
                                     alt="QR Code" style="border: 1px solid #ddd; border-radius: 8px;">
                                <br><small>QR kod (online generator)</small>
                            </div>
                        `;
                    } else {
                        qrContainer.innerHTML = '';
                        qrContainer.appendChild(canvas);
                    }
                });
            } catch (error) {
                console.error('QR kod yaradılma xətası:', error);
                // Fallback: online QR generator
                qrContainer.innerHTML = `
                    <div style="text-align: center;">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(SITE_URL)}"
                             alt="QR Code" style="border: 1px solid #ddd; border-radius: 8px;">
                        <br><small>QR kod (online generator)</small>
                    </div>
                `;
            }
        }

        // QR kodu yüklə
        function downloadQR() {
            if (typeof QRCode === 'undefined') {
                // Fallback: online generator ilə yüklə
                const downloadUrl = `https://api.qrserver.com/v1/create-qr-code/?size=400x400&format=png&data=${encodeURIComponent(SITE_URL)}`;
                const link = document.createElement('a');
                link.download = 'brendhouse-qr-code.png';
                link.href = downloadUrl;
                link.target = '_blank';
                link.click();
                return;
            }

            try {
                QRCode.toDataURL(SITE_URL, {
                    width: 400,
                    height: 400,
                    margin: 2
                }, function (error, url) {
                    if (error) {
                        console.error('QR kod yüklənmə xətası:', error);
                        // Fallback: online generator
                        const downloadUrl = `https://api.qrserver.com/v1/create-qr-code/?size=400x400&format=png&data=${encodeURIComponent(SITE_URL)}`;
                        const link = document.createElement('a');
                        link.download = 'brendhouse-qr-code.png';
                        link.href = downloadUrl;
                        link.target = '_blank';
                        link.click();
                    } else {
                        const link = document.createElement('a');
                        link.download = 'brendhouse-qr-code.png';
                        link.href = url;
                        link.click();
                    }
                });
            } catch (error) {
                console.error('QR kod yüklənmə xətası:', error);
                // Fallback: online generator
                const downloadUrl = `https://api.qrserver.com/v1/create-qr-code/?size=400x400&format=png&data=${encodeURIComponent(SITE_URL)}`;
                const link = document.createElement('a');
                link.download = 'brendhouse-qr-code.png';
                link.href = downloadUrl;
                link.target = '_blank';
                link.click();
            }
        }

        // URL-i kopyala
        function copyURL() {
            navigator.clipboard.writeText(SITE_URL).then(() => {
                alert('Link kopyalandı!');
            });
        }

        // Analytics məlumatlarını yüklə
        function loadAnalyticsData() {
            // Local storage-dan məlumatları al
            const data = getLocalAnalyticsData();
            
            // Statistikaları yenilə
            updateStats(data);
            updateLinkStats(data);
            updateActivityTable(data);
        }

        // Local storage-dan analytics məlumatlarını al
        function getLocalAnalyticsData() {
            const categories = ['page_views', 'link_clicks', 'session_ends', 'theme_changes'];
            const data = {};
            
            categories.forEach(category => {
                const key = `brendhouse_analytics_${category}`;
                data[category] = JSON.parse(localStorage.getItem(key) || '[]');
            });
            
            return data;
        }

        // Statistikaları yenilə
        function updateStats(data) {
            document.getElementById('pageViews').textContent = data.page_views.length;
            document.getElementById('linkClicks').textContent = data.link_clicks.length;
            
            // Aktiv sessionları hesabla (son 30 dəqiqə)
            const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
            const activeSessions = new Set(
                data.page_views
                    .filter(view => view.timestamp > thirtyMinutesAgo)
                    .map(view => view.sessionId)
            ).size;
            
            document.getElementById('activeSessions').textContent = activeSessions;
        }

        // Link statistikalarını yenilə
        function updateLinkStats(data) {
            const linkStats = {};
            const linkNames = {
                phone: '📞 Telefon',
                whatsapp: '💬 WhatsApp',
                website: '🌐 Veb Səhifə',
                instagram: '📸 Instagram',
                facebook: '👥 Facebook',
                tiktok: '🎵 TikTok',
                youtube: '📺 YouTube',
                linkedin: '💼 LinkedIn'
            };
            
            data.link_clicks.forEach(click => {
                linkStats[click.linkType] = (linkStats[click.linkType] || 0) + 1;
            });
            
            const container = document.getElementById('linkStats');
            container.innerHTML = '';
            
            Object.entries(linkNames).forEach(([key, name]) => {
                const count = linkStats[key] || 0;
                const div = document.createElement('div');
                div.className = 'link-stat';
                div.innerHTML = `
                    <div class="name">${name}</div>
                    <div class="count">${count}</div>
                `;
                container.appendChild(div);
            });
        }

        // Aktivitə cədvəlini yenilə
        function updateActivityTable(data) {
            const tbody = document.getElementById('activityTableBody');
            tbody.innerHTML = '';
            
            // Bütün aktivitələri birləşdir və vaxt üzrə sırala
            const allActivities = [
                ...data.page_views.map(item => ({...item, type: 'Səhifə Ziyarəti'})),
                ...data.link_clicks.map(item => ({...item, type: 'Link Klik'})),
                ...data.theme_changes.map(item => ({...item, type: 'Theme Dəyişikliyi'}))
            ].sort((a, b) => b.timestamp - a.timestamp).slice(0, 50); // Son 50 aktivitə
            
            allActivities.forEach(activity => {
                const row = document.createElement('tr');
                const date = new Date(activity.timestamp);
                const timeStr = date.toLocaleString('az-AZ');
                
                let details = '';
                if (activity.type === 'Link Klik') {
                    details = `${activity.linkType} - ${activity.linkText}`;
                } else if (activity.type === 'Theme Dəyişikliyi') {
                    details = `${activity.fromTheme} → ${activity.toTheme}`;
                } else {
                    details = activity.url || 'N/A';
                }
                
                row.innerHTML = `
                    <td class="timestamp">${timeStr}</td>
                    <td>${activity.type}</td>
                    <td>${details}</td>
                    <td><code>${activity.sessionId.substr(-8)}</code></td>
                `;
                tbody.appendChild(row);
            });
        }

        // Məlumatları yenilə
        function refreshData() {
            loadAnalyticsData();
            checkAPIStatus();
            alert('Məlumatlar yeniləndi!');
        }

        // Bütün məlumatları təmizlə
        function clearAllData() {
            if (confirm('Bütün analytics məlumatlarını təmizləmək istədiyinizə əminsiniz?')) {
                const categories = ['page_views', 'link_clicks', 'session_ends', 'theme_changes', 'failed_requests'];
                categories.forEach(category => {
                    localStorage.removeItem(`brendhouse_analytics_${category}`);
                });
                loadAnalyticsData();
                alert('Bütün məlumatlar təmizləndi!');
            }
        }

        // Məlumatları export et
        function exportData() {
            const data = getLocalAnalyticsData();
            const jsonData = JSON.stringify(data, null, 2);
            
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.download = `brendhouse-analytics-${new Date().toISOString().split('T')[0]}.json`;
            link.href = url;
            link.click();
            
            URL.revokeObjectURL(url);
        }

        // API statusunu yoxla
        async function checkAPIStatus() {
            const statusElement = document.getElementById('apiStatus');

            try {
                let apiUrl;
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    apiUrl = 'http://localhost:3009/health';
                } else if (window.location.hostname.includes('brendhouse.az')) {
                    apiUrl = '/api/health'; // cPanel proxy ilə
                } else {
                    apiUrl = '/health';
                }

                const response = await fetch(apiUrl);
                if (response.ok) {
                    const data = await response.json();
                    statusElement.innerHTML = '<span style="color: green;">✅ API Aktiv</span>';
                    console.log('API Status:', data);
                } else {
                    throw new Error('API cavab vermir');
                }
            } catch (error) {
                statusElement.innerHTML = '<span style="color: red;">❌ API Əlçatan Deyil (Local Storage istifadə edilir)</span>';
                console.error('API Status Error:', error);
            }
        }

        // Login sistemi
        const ADMIN_PASSWORD = 'brendhouse2024'; // Şifrəni dəyişin!
        const LOGIN_SESSION_KEY = 'brendhouse_admin_session';

        // Login yoxla
        function checkLogin() {
            const session = localStorage.getItem(LOGIN_SESSION_KEY);
            if (session) {
                const sessionData = JSON.parse(session);
                const now = Date.now();

                // Session 24 saat etibarlıdır
                if (now - sessionData.timestamp < 24 * 60 * 60 * 1000) {
                    showDashboard();
                    return true;
                }
            }

            showLoginModal();
            return false;
        }

        // Login modal göstər
        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'flex';
            document.getElementById('dashboard').style.display = 'none';
            document.getElementById('passwordInput').focus();
        }

        // Dashboard göstər
        function showDashboard() {
            document.getElementById('loginModal').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';

            // Dashboard məlumatlarını yüklə
            loadAnalyticsData();
            checkAPIStatus();

            // QR kod yaradılmasını bir az gecikdir
            setTimeout(() => {
                generateQR();
            }, 500);
        }

        // Login form submit
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Lockout yoxla
            const lockoutStatus = window.checkLoginAttempts();
            if (lockoutStatus.locked) {
                document.getElementById('loginError').textContent =
                    `Çox sayda yanlış cəhd! ${lockoutStatus.remainingMinutes} dəqiqə gözləyin.`;
                return;
            }

            const password = document.getElementById('passwordInput').value;
            const errorDiv = document.getElementById('loginError');

            if (password === ADMIN_PASSWORD) {
                // Uğurlu login
                window.recordSuccessfulLogin();

                // Session yarat
                const sessionData = {
                    timestamp: Date.now(),
                    user: 'admin'
                };
                localStorage.setItem(LOGIN_SESSION_KEY, JSON.stringify(sessionData));

                // Security log
                if (typeof adminSecurity !== 'undefined') {
                    adminSecurity.logSecurityEvent('successful_login');
                }

                // Dashboard göstər
                showDashboard();

                // Input təmizlə
                document.getElementById('passwordInput').value = '';
                errorDiv.textContent = '';

            } else {
                // Uğursuz login
                const failedStatus = window.recordFailedLogin();

                // Security log
                if (typeof adminSecurity !== 'undefined') {
                    adminSecurity.logSecurityEvent('failed_login', {
                        attemptsRemaining: failedStatus.attemptsRemaining
                    });
                }

                if (failedStatus.locked) {
                    errorDiv.textContent =
                        `Çox sayda yanlış cəhd! ${failedStatus.remainingMinutes} dəqiqə gözləyin.`;
                } else {
                    errorDiv.textContent =
                        `Yanlış şifrə! ${failedStatus.attemptsRemaining} cəhd qalıb.`;
                }

                document.getElementById('passwordInput').value = '';
                document.getElementById('passwordInput').focus();

                // 5 saniyə sonra xəta mesajını gizlət
                setTimeout(() => {
                    errorDiv.textContent = '';
                }, 5000);
            }
        });

        // Logout funksiyası
        function logout() {
            if (confirm('Admin paneldən çıxmaq istədiyinizə əminsiniz?')) {
                // Security log
                if (typeof adminSecurity !== 'undefined') {
                    adminSecurity.logSecurityEvent('logout');
                }

                localStorage.removeItem(LOGIN_SESSION_KEY);
                showLoginModal();
            }
        }

        // Enter key ilə login
        document.getElementById('passwordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });

        // Səhifə yüklənəndə
        document.addEventListener('DOMContentLoaded', function() {
            // Əvvəlcə login yoxla
            if (checkLogin()) {
                // QR kod yaradılmasını bir az gecikdir (library yüklənsin deyə)
                setTimeout(() => {
                    generateQR();
                }, 1000);

                // Hər 30 saniyədə bir yenilə
                setInterval(loadAnalyticsData, 30000);
            }
        });
    </script>
</body>
</html>
