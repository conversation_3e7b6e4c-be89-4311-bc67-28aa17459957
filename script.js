// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('Brendhouse Social Page Loaded');

    // Update current year
    updateCurrentYear();

    // Add click animations
    addClickAnimations();

    // Handle logo placeholder
    handleLogoPlaceholder();

    // Add hover effects
    addHoverEffects();

    // Initialize theme
    initializeTheme();

    // Add theme toggle functionality
    setupThemeToggle();
});

// Update current year automatically
function updateCurrentYear() {
    const currentYear = new Date().getFullYear();
    const yearElement = document.getElementById('current-year');

    if (yearElement) {
        yearElement.textContent = currentYear;
    }
}

// Simple click animations
function addClickAnimations() {
    const buttons = document.querySelectorAll('.contact-btn, .social-btn');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// Handle logo placeholder if image doesn't exist
function handleLogoPlaceholder() {
    const logo = document.getElementById('logo');
    
    logo.addEventListener('error', function() {
        // Create a text placeholder if logo image doesn't exist
        const placeholder = document.createElement('div');
        placeholder.className = 'logo-placeholder';
        placeholder.innerHTML = '<h1>BRENDHOUSE</h1>';
        placeholder.style.cssText = `
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            font-family: 'Arial Black', sans-serif;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 2px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        `;
        
        // Add hover effect to placeholder
        placeholder.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        
        placeholder.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        this.parentNode.replaceChild(placeholder, this);
    });
}

// Simple hover effects
function addHoverEffects() {
    // CSS handles hover effects now
}







// Initialize theme based on user preference or system preference
function initializeTheme() {
    const savedTheme = localStorage.getItem('brendhouse-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (systemPrefersDark) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
    }
}

// Setup theme toggle functionality
function setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');

    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Simple click animation
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);

        // Change theme
        document.documentElement.setAttribute('data-theme', newTheme);

        // Save theme preference
        localStorage.setItem('brendhouse-theme', newTheme);
    });
}

// Listen for system theme changes
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
    if (!localStorage.getItem('brendhouse-theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', newTheme);
    }
});
