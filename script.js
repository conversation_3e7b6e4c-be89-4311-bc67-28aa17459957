// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('Brendhouse Social Page Loaded');

    // Update current year
    updateCurrentYear();

    // Add click animations
    addClickAnimations();

    // Handle logo placeholder
    handleLogoPlaceholder();

    // Add hover effects
    addHoverEffects();

    // Add mouse move effects
    addMouseMoveEffects();

    // Add loading animation
    addLoadingAnimation();

    // Initialize theme
    initializeTheme();

    // Add theme toggle functionality
    setupThemeToggle();

    // Add performance optimizations
    addPerformanceOptimizations();

    // Add keyboard shortcuts
    addKeyboardShortcuts();
});

// Update current year automatically
function updateCurrentYear() {
    const currentYear = new Date().getFullYear();
    const yearElement = document.getElementById('current-year');

    if (yearElement) {
        yearElement.textContent = currentYear;
    }
}

// Add click animations to buttons
function addClickAnimations() {
    const buttons = document.querySelectorAll('.contact-btn, .social-btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            // Remove ripple after animation
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Handle logo placeholder if image doesn't exist
function handleLogoPlaceholder() {
    const logo = document.getElementById('logo');
    
    logo.addEventListener('error', function() {
        // Create a text placeholder if logo image doesn't exist
        const placeholder = document.createElement('div');
        placeholder.className = 'logo-placeholder';
        placeholder.innerHTML = '<h1>BRENDHOUSE</h1>';
        placeholder.style.cssText = `
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            font-family: 'Arial Black', sans-serif;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 2px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        `;
        
        // Add hover effect to placeholder
        placeholder.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        
        placeholder.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        this.parentNode.replaceChild(placeholder, this);
    });
}

// Add enhanced hover effects
function addHoverEffects() {
    const socialBtns = document.querySelectorAll('.social-btn');
    
    socialBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Phone number formatting and validation
function formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Format as +994 XX XXX XX XX
    if (cleaned.length === 12 && cleaned.startsWith('994')) {
        return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8, 10)} ${cleaned.slice(10)}`;
    }
    
    return phoneNumber;
}

// Track clicks for analytics (optional)
function trackClick(platform) {
    console.log(`User clicked on ${platform}`);
    
    // Here you can add analytics tracking code
    // Example: Google Analytics, Facebook Pixel, etc.
    
    // Show a brief feedback
    showClickFeedback(platform);
}

// Show click feedback
function showClickFeedback(platform) {
    const feedback = document.createElement('div');
    feedback.textContent = `${platform} açılır...`;
    feedback.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 14px;
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(feedback);
    
    setTimeout(() => {
        feedback.remove();
    }, 2000);
}

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .contact-btn, .social-btn {
        position: relative;
        overflow: hidden;
    }
`;

document.head.appendChild(style);

// Add mouse move effects for container
function addMouseMoveEffects() {
    const container = document.querySelector('.container');

    container.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / 20;
        const rotateY = (centerX - x) / 20;

        this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px)`;
    });

    container.addEventListener('mouseleave', function() {
        this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateY(0px)';
    });
}

// Add loading animation
function addLoadingAnimation() {
    const container = document.querySelector('.container');
    const elements = container.querySelectorAll('.logo-section, .contact-section, .social-section, .footer');

    // Initially hide elements
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

        // Animate in with delay
        setTimeout(() => {
            el.style.opacity = '1';
            el.style.transform = 'translateY(0px)';
        }, 200 + (index * 150));
    });
}

// Add click sound effect (optional)
function playClickSound() {
    // Create audio context for click sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
}

// Initialize theme based on user preference or system preference
function initializeTheme() {
    const savedTheme = localStorage.getItem('brendhouse-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    } else if (systemPrefersDark) {
        document.documentElement.setAttribute('data-theme', 'dark');
    } else {
        document.documentElement.setAttribute('data-theme', 'light');
    }
}

// Setup theme toggle functionality
function setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');

    themeToggle.addEventListener('click', function() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        // Add click animation
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
            this.style.transform = '';
        }, 150);

        // Change theme with animation
        document.documentElement.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        document.documentElement.setAttribute('data-theme', newTheme);

        // Save theme preference
        localStorage.setItem('brendhouse-theme', newTheme);

        // Add theme change effect
        createThemeChangeEffect();

        // Optional: Play sound effect
        try {
            playThemeChangeSound();
        } catch (e) {
            // Ignore audio errors
        }
    });
}

// Create visual effect when theme changes
function createThemeChangeEffect() {
    const effect = document.createElement('div');
    effect.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        pointer-events: none;
        z-index: 9999;
        animation: themeChangeRipple 0.8s ease-out;
    `;

    document.body.appendChild(effect);

    setTimeout(() => {
        effect.remove();
    }, 800);
}

// Play theme change sound
function playThemeChangeSound() {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.2);

    gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
}

// Listen for system theme changes
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
    if (!localStorage.getItem('brendhouse-theme')) {
        const newTheme = e.matches ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', newTheme);
        createThemeChangeEffect();
    }
});

// Add performance optimizations
function addPerformanceOptimizations() {
    // Reduce motion for users who prefer it
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Optimize animations on low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.documentElement.style.setProperty('--animation-speed', '0.5s');
    }
}

// Add keyboard shortcuts
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Toggle theme with 'T' key
        if (e.key.toLowerCase() === 't' && !e.ctrlKey && !e.altKey && !e.metaKey) {
            const activeElement = document.activeElement;
            if (activeElement.tagName !== 'INPUT' && activeElement.tagName !== 'TEXTAREA') {
                document.getElementById('themeToggle').click();
                e.preventDefault();
            }
        }

        // Focus on phone button with 'P' key
        if (e.key.toLowerCase() === 'p' && !e.ctrlKey && !e.altKey && !e.metaKey) {
            const phoneBtn = document.querySelector('.phone-btn');
            if (phoneBtn) {
                phoneBtn.focus();
                e.preventDefault();
            }
        }

        // Focus on WhatsApp button with 'W' key
        if (e.key.toLowerCase() === 'w' && !e.ctrlKey && !e.altKey && !e.metaKey) {
            const whatsappBtn = document.querySelector('.whatsapp-btn');
            if (whatsappBtn) {
                whatsappBtn.focus();
                e.preventDefault();
            }
        }
    });
}

// Add intersection observer for scroll animations
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    const animatedElements = document.querySelectorAll('.social-btn, .contact-btn');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}
